# 阿里云OSS存储功能设计文档

## 概述

本设计文档旨在规划将现有的本地文件存储系统迁移到阿里云对象存储服务(OSS)的实现方案。当前系统使用本地文件系统存储上传的照片，通过此改造，我们将利用阿里云OSS的高可用性和可扩展性来存储文件内容，同时保持元数据在本地数据库中管理。

### 功能说明
将系统中原有的本地文件存储方式替换为阿里云OSS存储，实现文件内容与元数据的分离存储，提升系统的可扩展性和可靠性。

### 使用场景
- 大规模照片文件存储
- 高并发访问场景
- 需要CDN加速的文件分发
- 降低本地存储压力

### 优势特点
- 高可用性：利用阿里云OSS的99.999999999%的数据可靠性
- 高扩展性：无需担心存储容量限制
- 成本优化：按实际使用量付费
- 安全性：通过访问控制和加密保障数据安全

## 架构设计

### 当前架构
- 文件内容存储在本地文件系统中
- 元数据(文件名、大小、上传时间等)存储在H2数据库中
- Photo实体通过storagePath字段指向本地文件路径

### 目标架构
- 文件内容存储在阿里云OSS中
- 元数据继续存储在H2数据库中
- Photo实体通过ossKey字段标识OSS中的文件位置
- 引入OSSService封装所有OSS相关操作
- 通过OSSConfig管理OSS客户端配置

### 架构图
```mermaid
graph TD
    A[客户端] -->|HTTP请求| B[PhotoController]
    B -->|调用| C[PhotoService]
    C -->|文件操作| D[OSSService]
    C -->|元数据操作| E[PhotoRepository]
    D -->|OSS API| F[阿里云OSS]
    E -->|数据库操作| G[H2数据库]
```

## 技术实现

### 1. 添加依赖
在pom.xml中添加阿里云OSS SDK依赖：
```xml
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>aliyun-java-sdk-core</artifactId>
    <version>4.5.30</version>
</dependency>
<dependency>
    <groupId>com.aliyun.oss</groupId>
    <artifactId>aliyun-sdk-oss</artifactId>
    <version>3.10.2</version>
</dependency>
```

### 2. 配置管理
在application.yml中添加OSS配置项：
```yaml
aliyun:
  oss:
    endpoint: ${ALIYUN_OSS_ENDPOINT:oss-cn-hangzhou.aliyuncs.com}
    access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}
    bucket-name: ${ALIYUN_OSS_BUCKET:photo-storage-bucket}
    base-url: ${ALIYUN_OSS_BASE_URL:https://photo-storage-bucket.oss-cn-hangzhou.aliyuncs.com}
```

创建OSSConfig配置类管理OSS客户端Bean：
```java
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
@Data
public class OSSConfig {
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private String baseUrl;
    
    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }
}
```

### 3. 核心服务
创建OSSService封装文件上传、下载、删除等操作：

#### 方法定义
- uploadFile(MultipartFile file): String - 上传文件并返回OSS键名
- downloadFile(String ossKey): byte[] - 下载文件
- deleteFile(String ossKey): void - 删除文件
- fileExists(String ossKey): boolean - 检查文件是否存在
- getFileUrl(String ossKey): String - 获取文件访问URL
- generateOssKey(String originalFilename): String - 生成唯一的OSS键名

#### OSS键名生成规则
文件上传时，系统会生成唯一的OSS对象键名，格式为：
```
photos/{year}/{month}/{uuid}.{extension}
```

例如：`photos/2024/01/abc123def456ghi789jkl012.jpg`

#### 异常处理
OSSService需要处理以下异常情况：
- 网络连接异常
- 权限不足
- 存储空间不足
- 文件不存在
- 服务器内部错误

所有OSS相关异常应转换为自定义PhotoException并包含相应的错误码和信息。

### 4. 数据模型调整
修改Photo实体，将storagePath字段改为ossKey字段：
```java
@Column(nullable = false)
private String ossKey;
```

### 5. 服务层调整
修改PhotoService以使用OSSService替代本地文件操作：
- 在upload方法中调用OSSService.uploadFile()上传文件
- 在download方法中调用OSSService.downloadFile()下载文件
- 在deletePhoto方法中调用OSSService.deleteFile()删除文件

### 6. 环境变量
设置必要的环境变量：
```bash
export ALIYUN_ACCESS_KEY_ID=your-access-key-id
export ALIYUN_ACCESS_KEY_SECRET=your-access-key-secret
export ALIYUN_OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
export ALIYUN_OSS_BUCKET=your-bucket-name
```

## 测试策略

### 单元测试
- 为OSSService编写单元测试，验证文件上传、下载、删除等功能
- 使用Mockito模拟OSS客户端行为
- 更新PhotoServiceTest以适配新的OSS存储方式

### 集成测试
- 验证完整的文件上传和下载流程
- 测试异常情况处理，如网络中断、权限不足等
- 验证文件元数据正确保存和检索

### 配置测试
- 验证环境变量正确加载
- 测试不同OSS配置下的兼容性
- 验证配置属性绑定正确性

### 性能测试
- 测试大文件上传下载性能
- 验证并发访问下的系统稳定性

## 上传流程序列图

```mermaid
sequenceDiagram
    participant Client as "客户端"
    participant Controller as "PhotoController"
    participant PhotoService as "PhotoService"
    participant OSSService as "OSSService"
    participant OSS as "阿里云OSS"
    
    Client->>Controller : POST /api/photos/upload
    Controller->>PhotoService : upload(file)
    PhotoService->>PhotoService : validateFile(file)
    PhotoService->>OSSService : uploadFile(file)
    OSSService->>OSSService : generateOssKey(file.getOriginalFilename())
    OSSService->>OSS : putObject(bucketName, ossKey, file.getInputStream())
    OSS-->>OSSService : 上传结果
    OSSService-->>PhotoService : 返回ossKey
    PhotoService->>PhotoService : 创建Photo实体(包含ossKey)
    PhotoService->>PhotoRepository : save(photo)
    PhotoRepository-->>PhotoService : 保存结果
    PhotoService-->>Controller : 返回Photo对象
    Controller-->>Client : 200 OK + Photo数据
```

## 部署与配置

### 环境变量配置
在生产环境中，应通过环境变量配置敏感信息：

```bash
# 阿里云访问密钥
export ALIYUN_ACCESS_KEY_ID=your-access-key-id
export ALIYUN_ACCESS_KEY_SECRET=your-access-key-secret

# OSS配置
export ALIYUN_OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
export ALIYUN_OSS_BUCKET=your-production-bucket
```
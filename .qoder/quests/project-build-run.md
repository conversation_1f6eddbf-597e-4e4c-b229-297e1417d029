# 照片上传下载系统 - 项目构建与运行设计

## 概述

本文档详细描述了照片上传下载系统的构建、配置和运行流程。该系统基于 Spring Boot 3.2.0 构建，使用 H2 内存数据库进行数据存储，并集成了图片处理和安全认证功能。

## 系统架构

```mermaid
graph TB
    A[用户请求] --> B[Spring Security]
    B --> C[PhotoController]
    C --> D[PhotoService]
    D --> E[PhotoRepository]
    D --> F[FileUtils]
    E --> G[H2 Database]
    F --> H[本地文件系统]
    
    subgraph "Spring Boot 应用层"
        C
        D
        I[GlobalExceptionHandler]
    end
    
    subgraph "数据持久层"
        E
        G
    end
    
    subgraph "文件存储层"
        F
        H
    end
    
    J[图片处理] --> K[imgscalr]
    D --> J
```

## 技术栈

| 组件 | 版本 | 用途 |
|------|------|------|
| Spring Boot | 3.2.0 | 核心框架 |
| Java | 17+ | 编程语言 |
| Maven | 3.x | 构建工具 |
| H2 Database | - | 内存数据库 |
| Spring Security | - | 安全认证 |
| imgscalr | 4.2 | 图片处理 |
| Gson | - | JSON处理 |

## 环境准备

### 系统要求

- **JDK**: 17 或更高版本
- **Maven**: 3.6.0 或更高版本
- **内存**: 至少 512MB 可用内存
- **磁盘**: 至少 100MB 可用空间（用于文件上传存储）

### 环境验证

```bash
# 验证 Java 版本
java -version
# 预期输出: openjdk version "17.x.x"

# 验证 Maven 版本
mvn -version
# 预期输出: Apache Maven 3.x.x
```

## 项目结构

```
src/
├── main/
│   ├── java/com/example/
│   │   ├── config/
│   │   │   └── GlobalExceptionHandler.java    # 全局异常处理
│   │   ├── controller/
│   │   │   └── PhotoController.java           # REST API 控制器
│   │   ├── entity/
│   │   │   └── Photo.java                     # 照片实体类
│   │   ├── exception/
│   │   │   └── PhotoException.java            # 自定义异常
│   │   ├── repository/
│   │   │   └── PhotoRepository.java           # 数据访问层
│   │   ├── service/
│   │   │   └── PhotoService.java              # 业务逻辑层
│   │   ├── utils/
│   │   │   └── FileUtils.java                 # 文件工具类
│   │   └── PhotoUploadDownloadApplication.java # 主启动类
│   └── resources/
│       └── application.yml                     # 应用配置
└── test/
    └── java/com/example/
        ├── controller/
        │   └── PhotoControllerTest.java        # 控制器测试
        └── PhotoUploadDownloadApplicationTests.java # 应用测试
```

## 配置管理

### 核心配置 (application.yml)

```yaml
server:
  port: 8080                    # 服务端口
  servlet:
    multipart:
      max-file-size: 10MB       # 单文件最大大小
      max-request-size: 10MB    # 请求最大大小

spring:
  profiles:
    active: dev                 # 激活开发环境

photo:
  upload:
    path: ${user.home}/photo-upload  # 文件上传路径
    allowed-types: png,jpg,jpeg,gif  # 允许的文件类型
    max-size: 10485760              # 最大文件大小（字节）
  cache:
    enabled: true               # 是否启用缓存
    max-size: 100              # 最大缓存文件数
    timeout: 3600              # 缓存过期时间（秒）

security:
  basic:
    enabled: true              # 启用基础认证
    password: ${RANDOM_PASSWORD} # 随机密码

logging:
  level:
    com.example: DEBUG         # 应用日志级别
    org.springframework.web: INFO # Spring Web 日志级别
```

### 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `photo.upload.path` | 文件上传存储路径 | `${user.home}/photo-upload` |
| `photo.upload.allowed-types` | 允许上传的文件类型 | `png,jpg,jpeg,gif` |
| `photo.upload.max-size` | 最大文件大小（字节） | `10485760` (10MB) |
| `security.basic.password` | 基础认证密码 | 随机生成 |

## 构建流程

### 1. 项目导入

```bash
# 克隆项目（如果从 Git 仓库获取）
git clone <repository-url>
cd newproject

# 或直接使用现有项目目录
cd /Users/<USER>/code/newproject
```

### 2. 依赖下载

```bash
# 清理并下载依赖
mvn clean compile

# 输出示例：
# [INFO] --- maven-compiler-plugin:3.11.0:compile (default-compile) @ photo-upload-download ---
# [INFO] Changes detected - recompiling the module!
# [INFO] Compiling 8 source files to target/classes
```

### 3. 项目编译

```bash
# 编译项目
mvn compile

# 验证编译结果
ls target/classes/com/example/
```

### 4. 运行测试

```bash
# 执行单元测试
mvn test

# 预期输出：
# [INFO] Tests run: X, Failures: 0, Errors: 0, Skipped: 0
```

### 5. 打包应用

```bash
# 打包为可执行 JAR
mvn package

# 生成的文件位置
ls target/photo-upload-download-1.0.0.jar
```

## 运行方式

### 方式一：Maven 运行

```bash
# 使用 Maven 插件运行
mvn spring-boot:run

# 预期输出：
#   .   ____          _            __ _ _
#  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
# ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
#  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
#   '  |____| .__|_| |_|_| |_\__, | / / / /
#  =========|_|==============|___/=/_/_/_/
#  :: Spring Boot ::                (v3.2.0)
```

### 方式二：JAR 文件运行

```bash
# 运行打包后的 JAR 文件
java -jar target/photo-upload-download-1.0.0.jar

# 带参数运行
java -jar target/photo-upload-download-1.0.0.jar --server.port=8081
```

### 方式三：IDE 运行

在 IDE 中直接运行 `PhotoUploadDownloadApplication.java` 主类。

## 健康检查

### 应用启动验证

```bash
# 检查应用是否启动成功
curl http://localhost:8080/actuator/health

# 预期响应：
# {"status":"UP"}
```

### 数据库连接验证

```bash
# 访问 H2 控制台（如果启用）
curl http://localhost:8080/h2-console
```

## API 接口测试

### 1. 上传照片

```bash
curl -X POST "http://localhost:8080/api/photos/upload" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@test-image.jpg" \
     -u admin:password

# 预期响应：
# {
#   "id": 1,
#   "fileName": "test-image.jpg",
#   "fileSize": 12345,
#   "uploadTime": "2024-01-01T10:00:00"
# }
```

### 2. 下载照片

```bash
curl -X GET "http://localhost:8080/api/photos/download/1" \
     -u admin:password \
     -o downloaded-image.jpg
```

### 3. 查看照片列表

```bash
curl -X GET "http://localhost:8080/api/photos" \
     -u admin:password

# 预期响应：
# [
#   {
#     "id": 1,
#     "fileName": "test-image.jpg",
#     "fileSize": 12345,
#     "uploadTime": "2024-01-01T10:00:00"
#   }
# ]
```

## 故障排查

### 常见问题及解决方案

#### 1. 端口占用错误

**错误信息**:
```
Web server failed to start. Port 8080 was already in use.
```

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :8080

# 终止进程或更换端口
java -jar app.jar --server.port=8081
```

#### 2. 文件上传失败

**错误信息**:
```
Maximum upload size exceeded
```

**解决方案**:
- 检查 `application.yml` 中的 `max-file-size` 配置
- 确保上传文件大小不超过 10MB

#### 3. 权限认证失败

**错误信息**:
```
401 Unauthorized
```

**解决方案**:
- 检查启动日志中的随机密码
- 使用正确的用户名密码进行认证

### 日志查看

```bash
# 查看应用日志
tail -f logs/application.log

# 查看启动时的随机密码
grep "password" logs/application.log
```

## 性能监控

### 内存使用

```bash
# 查看 JVM 内存使用情况
jstat -gc <pid>
```

### 磁盘使用

```bash
# 查看上传目录大小
du -sh ~/photo-upload
```

## 部署建议

### 生产环境配置

1. **数据库配置**: 替换 H2 为生产级数据库（MySQL/PostgreSQL）
2. **文件存储**: 考虑使用云存储服务（OSS/S3）
3. **安全配置**: 使用更强的认证机制
4. **监控**: 集成 Actuator 和监控工具

### 容器化部署

```dockerfile
FROM openjdk:17-jre-slim
COPY target/photo-upload-download-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 测试策略

### 单元测试

- **PhotoController**: API 接口测试
- **PhotoService**: 业务逻辑测试
- **FileUtils**: 文件处理工具测试

### 集成测试

- 完整的文件上传下载流程测试
- 数据库集成测试
- 安全认证集成测试


package com.example.controller;

import com.example.entity.Photo;
import com.example.service.PhotoService;
import com.example.utils.FileUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/photos")
@RequiredArgsConstructor
public class PhotoController {

    private final PhotoService photoService;

    @PostMapping("/upload")
    public ResponseEntity<Photo> uploadSingle(@RequestParam("file") MultipartFile file) {
        try {
            Photo photo = photoService.upload(file);
            return ResponseEntity.ok(photo);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    @PostMapping("/upload/batch")
    public ResponseEntity<List<Photo>> uploadMultiple(@RequestParam("files") MultipartFile[] files) {
        try {
            List<Photo> photos = photoService.uploadMultiple(files);
            return ResponseEntity.ok(photos);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    @GetMapping("/download/{fileName}")
    public ResponseEntity<byte[]> download(@PathVariable String fileName) {
        try {
            byte[] fileBytes = photoService.download(fileName);
            Photo photo = photoService.getPhotoByFileName(fileName);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(photo.getContentType()));
            headers.setContentDisposition(ContentDisposition.attachment().filename(photo.getOriginalName()).build());
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileBytes);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(null);
        }
    }

    @GetMapping("/preview/{fileName}")
    public ResponseEntity<byte[]> preview(@PathVariable String fileName) {
        try {
            byte[] fileBytes = photoService.download(fileName);
            Photo photo = photoService.getPhotoByFileName(fileName);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(photo.getContentType()));
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileBytes);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(null);
        }
    }

    @GetMapping("/public")
    public ResponseEntity<List<Photo>> getPublicPhotos() {
        List<Photo> photos = photoService.getPublicPhotos();
        return ResponseEntity.ok(photos);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePhoto(@PathVariable Long id) {
        photoService.deletePhoto(id);
        return ResponseEntity.noContent().build();
    }
}

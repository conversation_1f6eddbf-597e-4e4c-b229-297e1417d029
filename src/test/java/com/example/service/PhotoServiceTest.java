package com.example.service;

import com.example.entity.Photo;
import com.example.exception.PhotoException;
import com.example.repository.PhotoRepository;
import com.example.utils.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 照片服务测试类
 * 测试PhotoService的所有核心功能，包括文件上传、下载、删除等操作
 * 使用Mockito框架进行单元测试，模拟依赖的Repository和OSSService
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class PhotoServiceTest {

    /** 模拟照片数据访问层 */
    @Mock
    private PhotoRepository photoRepository;

    @Mock
    private OSSService ossService;
    
    /** 被测试的照片服务，自动注入Mock对象 */
    @InjectMocks
    private PhotoService photoService;

    /** 测试用的模拟文件 */
    private MockMultipartFile testFile;
    /** 测试用的照片实体对象 */
    private Photo testPhoto;

    /**
     * 测试前置设置
     * 初始化测试环境，包括配置参数、测试文件和测试数据
     */
    @BeforeEach
    void setUp() {
        // 设置配置属性 - 使用反射设置私有字段
        ReflectionTestUtils.setField(photoService, "allowedTypes", "png,jpg,jpeg,gif");
        ReflectionTestUtils.setField(photoService, "maxSize", 10485760L); // 10MB
        
        // 创建测试文件 - 模拟用户上传的图片文件
        testFile = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );
        
        // 创建测试Photo对象 - 模拟数据库中的照片记录
        testPhoto = new Photo();
        testPhoto.setId(1L);
        testPhoto.setOriginalName("test.jpg");          // 原始文件名
        testPhoto.setFileName("uuid123.jpg");           // 服务器生成的文件名
        testPhoto.setContentType("image/jpeg");         // 文件类型
        testPhoto.setSize(1024L);                       // 文件大小
        testPhoto.setUploadTime(LocalDateTime.now());   // 上传时间
        testPhoto.setUploadIp("127.0.0.1");            // 上传IP
        testPhoto.setOssKey("photos/2024/01/uuid123.jpg"); // OSS存储路径
        testPhoto.setPublic(false);                     // 是否公开
        testPhoto.setDownloadCount(0);                  // 下载次数
        testPhoto.setDeleted(false);                    // 是否删除
    }

    /**
     * 测试文件上传成功场景
     * 验证点：
     * 1. 文件成功上传到OSS
     * 2. 照片信息正确保存到数据库
     * 3. 返回完整的照片对象
     */
    @Test
    void upload_Success() throws PhotoException {
        // Given - 准备测试数据和Mock行为
        String ossKey = "photos/2024/01/uuid123.jpg";
        when(ossService.uploadFile(testFile)).thenReturn(ossKey);           // 模拟OSS上传成功
        when(photoRepository.save(any(Photo.class))).thenReturn(testPhoto); // 模拟数据库保存成功
        
        // 使用静态Mock模拟FileUtils工具类
        try (MockedStatic<FileUtils> fileUtilsMock = mockStatic(FileUtils.class)) {
            fileUtilsMock.when(() -> FileUtils.isImage("image/jpeg")).thenReturn(true); // 模拟文件类型验证
            fileUtilsMock.when(FileUtils::getRemoteIp).thenReturn("127.0.0.1");        // 模拟IP获取
            
            // When - 执行被测试的方法
            Photo result = photoService.upload(testFile);
            
            // Then - 验证结果和方法调用
            assertNotNull(result);                           // 验证返回结果不为空
            verify(ossService).uploadFile(testFile);         // 验证OSS上传方法被调用
            verify(photoRepository).save(any(Photo.class));  // 验证数据库保存方法被调用
        }
    }

    /**
     * 测试上传空文件异常场景
     * 验证点：
     * 1. 抛出PhotoException异常
     * 2. 异常消息正确
     * 3. 不调用OSS和数据库操作
     */
    @Test
    void upload_NullFile() {
        // When & Then - 执行测试并验证异常
        PhotoException exception = assertThrows(PhotoException.class, () -> 
            photoService.upload(null)
        );
        assertEquals("文件不能为空", exception.getMessage());      // 验证异常消息
        verify(ossService, never()).uploadFile(any());         // 验证OSS方法未被调用
        verify(photoRepository, never()).save(any());          // 验证数据库方法未被调用
    }

    /**
     * 测试上传空内容文件异常场景
     * 验证点：
     * 1. 文件内容为空时抛出异常
     * 2. 异常消息正确
     */
    @Test
    void upload_EmptyFile() {
        // Given - 创建空内容的文件
        MockMultipartFile emptyFile = new MockMultipartFile("file", "", "image/jpeg", new byte[0]);
        
        // When & Then - 执行测试并验证异常
        PhotoException exception = assertThrows(PhotoException.class, () -> 
            photoService.upload(emptyFile)
        );
        assertEquals("文件不能为空", exception.getMessage()); // 验证异常消息正确
    }

    /**
     * 测试上传超大文件异常场景
     * 验证点：
     * 1. 文件大小超过限制时抛出异常
     * 2. 异常消息正确
     */
    @Test
    void upload_FileTooLarge() {
        // Given - 创建超过大小限制的文件（11MB > 10MB限制）
        byte[] largeContent = new byte[11 * 1024 * 1024]; // 11MB
        MockMultipartFile largeFile = new MockMultipartFile("file", "large.jpg", "image/jpeg", largeContent);
        
        // When & Then - 执行测试并验证异常
        PhotoException exception = assertThrows(PhotoException.class, () -> 
            photoService.upload(largeFile)
        );
        assertEquals("文件大小超出限制", exception.getMessage()); // 验证异常消息正确
    }

    /**
     * 测试上传无效文件类型异常场景
     * 验证点：
     * 1. 非图片文件类型时抛出异常
     * 2. 异常消息正确
     */
    @Test
    void upload_InvalidFileType() {
        // Given - 创建非图片类型的文件
        MockMultipartFile textFile = new MockMultipartFile("file", "test.txt", "text/plain", "test content".getBytes());
        
        // 模拟FileUtils工具类的文件类型检查
        try (MockedStatic<FileUtils> fileUtilsMock = mockStatic(FileUtils.class)) {
            fileUtilsMock.when(() -> FileUtils.isImage("text/plain")).thenReturn(false); // 模拟非图片类型
            
            // When & Then - 执行测试并验证异常
            PhotoException exception = assertThrows(PhotoException.class, () -> 
                photoService.upload(textFile)
            );
            assertEquals("无效的文件类型", exception.getMessage()); // 验证异常消息正确
        }
    }

    /**
     * 测试批量文件上传成功场景
     * 验证点：
     * 1. 批量上传文件到OSS成功
     * 2. 所有照片信息正确保存到数据库
     * 3. 返回正确数量的照片列表
     */
    @Test
    void uploadMultiple_Success() throws PhotoException {
        // Given - 准备批量上传的文件数组
        MockMultipartFile[] files = {testFile};
        String ossKey = "photos/2024/01/uuid123.jpg";
        
        // 模拟OSS和数据库操作成功
        when(ossService.uploadFile(testFile)).thenReturn(ossKey);
        when(photoRepository.save(any(Photo.class))).thenReturn(testPhoto);
        
        // 模拟FileUtils工具类
        try (MockedStatic<FileUtils> fileUtilsMock = mockStatic(FileUtils.class)) {
            fileUtilsMock.when(() -> FileUtils.isImage("image/jpeg")).thenReturn(true); // 模拟文件类型验证
            fileUtilsMock.when(FileUtils::getRemoteIp).thenReturn("127.0.0.1");        // 模拟IP获取
            
            // When - 执行批量上传
            List<Photo> results = photoService.uploadMultiple(files);
            
            // Then - 验证批量上传结果
            assertNotNull(results);                          // 验证返回结果不为空
            assertEquals(1, results.size());                // 验证返回列表大小正确
            verify(ossService).uploadFile(testFile);         // 验证OSS上传方法被调用
            verify(photoRepository).save(any(Photo.class));  // 验证数据库保存方法被调用
        }
    }

    /**
     * 测试根据文件名获取照片成功场景
     * 验证点：
     * 1. 成功从数据库查询到照片
     * 2. 返回正确的照片对象
     * 3. 照片ID匹配
     */
    @Test
    void getPhotoByFileName_Success() {
        // Given - 准备查询的文件名和Mock返回结果
        String fileName = "uuid123.jpg";
        when(photoRepository.findByFileNameAndDeletedFalse(fileName)).thenReturn(Optional.of(testPhoto));
        
        // When - 执行根据文件名查询照片
        Photo result = photoService.getPhotoByFileName(fileName);
        
        // Then - 验证查询结果
        assertNotNull(result);                                         // 验证返回结果不为空
        assertEquals(testPhoto.getId(), result.getId());               // 验证照片ID正确
        verify(photoRepository).findByFileNameAndDeletedFalse(fileName); // 验证数据库查询方法被调用
    }

    /**
     * 测试根据文件名获取照片失败场景
     * 验证点：
     * 1. 文件不存在时抛出异常
     * 2. 异常消息包含"文件未找到"
     */
    @Test
    void getPhotoByFileName_NotFound() {
        // Given - 准备不存在的文件名，模拟数据库返回空结果
        String fileName = "nonexistent.jpg";
        when(photoRepository.findByFileNameAndDeletedFalse(fileName)).thenReturn(Optional.empty());
        
        // When & Then - 执行查询并验证异常
        PhotoException exception = assertThrows(PhotoException.class, () -> 
            photoService.getPhotoByFileName(fileName)
        );
        assertTrue(exception.getMessage().contains("文件未找到")); // 验证异常消息包含预期文字
    }

    @Test
    void download_Success() throws PhotoException {
        // Given
        String fileName = "uuid123.jpg";
        byte[] fileContent = "test content".getBytes();
        
        when(photoRepository.findByFileNameAndDeletedFalse(fileName)).thenReturn(Optional.of(testPhoto));
        when(ossService.downloadFile(testPhoto.getOssKey())).thenReturn(fileContent);
        when(photoRepository.save(any(Photo.class))).thenReturn(testPhoto);
        
        // When
        byte[] result = photoService.download(fileName);
        
        // Then
        assertNotNull(result);
        assertArrayEquals(fileContent, result);
        verify(ossService).downloadFile(testPhoto.getOssKey());
        verify(photoRepository).save(any(Photo.class));
        // 验证下载计数增加
        assertEquals(1, testPhoto.getDownloadCount());
    }

    @Test
    void download_FileNotFound() {
        // Given
        String fileName = "nonexistent.jpg";
        when(photoRepository.findByFileNameAndDeletedFalse(fileName)).thenReturn(Optional.empty());
        
        // When & Then
        PhotoException exception = assertThrows(PhotoException.class, () -> 
            photoService.download(fileName)
        );
        assertTrue(exception.getMessage().contains("文件未找到"));
        verify(ossService, never()).downloadFile(any());
    }

    @Test
    void getPublicPhotos() {
        // Given
        List<Photo> publicPhotos = List.of(testPhoto);
        when(photoRepository.findByDeletedFalseAndIsPublicTrue()).thenReturn(publicPhotos);
        
        // When
        List<Photo> result = photoService.getPublicPhotos();
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(photoRepository).findByDeletedFalseAndIsPublicTrue();
    }

    @Test
    void deletePhoto_Success() {
        // Given
        Long photoId = 1L;
        when(photoRepository.findById(photoId)).thenReturn(Optional.of(testPhoto));
        doNothing().when(ossService).deleteFile(testPhoto.getOssKey());
        when(photoRepository.save(any(Photo.class))).thenReturn(testPhoto);
        
        // When
        photoService.deletePhoto(photoId);
        
        // Then
        verify(ossService).deleteFile(testPhoto.getOssKey());
        verify(photoRepository).save(any(Photo.class));
        assertTrue(testPhoto.isDeleted());
    }

    @Test
    void deletePhoto_PhotoNotFound() {
        // Given
        Long photoId = 999L;
        when(photoRepository.findById(photoId)).thenReturn(Optional.empty());
        
        // When & Then
        PhotoException exception = assertThrows(PhotoException.class, () -> 
            photoService.deletePhoto(photoId)
        );
        assertEquals("照片未找到", exception.getMessage());
        verify(ossService, never()).deleteFile(any());
    }

    @Test
    void deletePhoto_OSSDeleteFails() {
        // Given
        Long photoId = 1L;
        when(photoRepository.findById(photoId)).thenReturn(Optional.of(testPhoto));
        doThrow(new PhotoException("OSS删除失败")).when(ossService).deleteFile(testPhoto.getOssKey());
        when(photoRepository.save(any(Photo.class))).thenReturn(testPhoto);
        
        // When
        photoService.deletePhoto(photoId);
        
        // Then
        verify(ossService).deleteFile(testPhoto.getOssKey());
        verify(photoRepository).save(any(Photo.class));
        assertTrue(testPhoto.isDeleted()); // 即使OSS删除失败，数据库记录仍应标记为删除
    }
}